/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        'naroop-background': '#FDFCF9',
        'naroop-surface': '#FFFFFF',
        'naroop-primary': '#FBBF24', // amber-400
        'naroop-secondary': '#34D399', // emerald-400
        'naroop-text-primary': '#1F2937',
        'naroop-text-secondary': '#6B7280',
        'naroop-dark-surface': '#27272A',
        'naroop-dark-text': '#F9FAFB',
      },
      fontFamily: {
        sans: ['Inter', 'sans-serif'],
      },
    },
  },
  plugins: [],
}
