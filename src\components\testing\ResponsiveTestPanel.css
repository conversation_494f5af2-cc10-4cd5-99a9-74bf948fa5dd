/* NAROOP Responsive Test Panel - Light Theme */

.responsive-test-panel {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.responsive-test-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(89, 28, 40, 0.5);
  backdrop-filter: blur(4px);
}

.responsive-test-content {
  position: relative;
  background: var(--color-heritage-cream, #FDFBF5);
  border: 2px solid var(--color-heritage-green, #6E8C65);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(89, 28, 40, 0.2);
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.responsive-test-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  background: linear-gradient(135deg, var(--color-heritage-yellow, #F7D046) 0%, var(--color-heritage-green, #6E8C65) 100%);
  border-bottom: 1px solid rgba(110, 140, 101, 0.2);
}

.responsive-test-header h2 {
  margin: 0;
  color: var(--color-heritage-maroon, #591C28);
  font-size: 1.5rem;
  font-weight: 700;
}

.responsive-test-close {
  background: transparent;
  border: none;
  color: var(--color-heritage-maroon, #591C28);
  font-size: 2rem;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.responsive-test-close:hover {
  background: rgba(89, 28, 40, 0.1);
}

.responsive-test-body {
  padding: 20px;
  overflow-y: auto;
  flex: 1;
}

.test-section {
  margin-bottom: 32px;
}

.test-section h3 {
  margin: 0 0 16px 0;
  color: var(--color-heritage-maroon, #591C28);
  font-size: 1.25rem;
  font-weight: 600;
}

.test-section h4 {
  margin: 0 0 12px 0;
  color: var(--color-heritage-green, #6E8C65);
  font-size: 1.125rem;
  font-weight: 600;
}

.test-section h5 {
  margin: 0 0 8px 0;
  color: var(--color-heritage-maroon, #591C28);
  font-size: 1rem;
  font-weight: 600;
}

/* Device Selector */
.device-selector {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.device-btn {
  background: rgba(110, 140, 101, 0.1);
  border: 2px solid rgba(110, 140, 101, 0.3);
  border-radius: 12px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-height: 44px;
}

.device-btn:hover {
  background: rgba(110, 140, 101, 0.2);
  border-color: var(--color-heritage-green, #6E8C65);
}

.device-btn--active {
  background: var(--color-heritage-yellow, #F7D046);
  border-color: var(--color-heritage-maroon, #591C28);
  color: var(--color-heritage-maroon, #591C28);
}

.device-dimensions {
  font-size: 0.875rem;
  color: var(--color-heritage-green, #6E8C65);
  opacity: 0.8;
}

/* Test Controls */
.test-run-btn {
  background: var(--color-heritage-yellow, #F7D046);
  border: 2px solid var(--color-heritage-maroon, #591C28);
  border-radius: 12px;
  padding: 12px 24px;
  color: var(--color-heritage-maroon, #591C28);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 44px;
}

.test-run-btn:hover:not(:disabled) {
  background: var(--color-heritage-green, #6E8C65);
  color: var(--color-heritage-cream, #FDFBF5);
}

.test-run-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Test Score */
.test-score {
  display: flex;
  justify-content: center;
  margin-bottom: 24px;
}

.score-circle {
  width: 120px;
  height: 120px;
  border: 4px solid;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(253, 251, 245, 0.9);
}

.score-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--color-heritage-maroon, #591C28);
}

.score-label {
  font-size: 0.875rem;
  color: var(--color-heritage-green, #6E8C65);
  text-align: center;
}

/* Test Summary */
.test-summary {
  background: rgba(110, 140, 101, 0.05);
  border: 1px solid rgba(110, 140, 101, 0.2);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 24px;
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  color: var(--color-heritage-green, #6E8C65);
  font-weight: 500;
}

.stat-value {
  font-weight: 600;
  color: var(--color-heritage-maroon, #591C28);
}

.stat-value--success {
  color: var(--color-heritage-green, #6E8C65);
}

.stat-value--error {
  color: var(--color-heritage-maroon, #591C28);
}

/* Test Breakdown */
.test-breakdown {
  margin-bottom: 24px;
}

.breakdown-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.breakdown-item {
  background: rgba(247, 208, 70, 0.1);
  border: 1px solid rgba(247, 208, 70, 0.3);
  border-radius: 12px;
  padding: 16px;
}

.breakdown-stats {
  display: flex;
  gap: 16px;
  margin-top: 8px;
}

.breakdown-passed {
  color: var(--color-heritage-green, #6E8C65);
  font-weight: 600;
}

.breakdown-failed {
  color: var(--color-heritage-maroon, #591C28);
  font-weight: 600;
}

/* Recommendations */
.test-recommendations {
  margin-bottom: 24px;
}

.recommendations-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.recommendation {
  background: rgba(110, 140, 101, 0.1);
  border-left: 4px solid;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 8px;
}

.recommendation--high {
  border-color: var(--color-heritage-maroon, #591C28);
  background: rgba(89, 28, 40, 0.1);
}

.recommendation--medium {
  border-color: var(--color-heritage-yellow, #F7D046);
  background: rgba(247, 208, 70, 0.1);
}

.recommendation--low {
  border-color: var(--color-heritage-green, #6E8C65);
  background: rgba(110, 140, 101, 0.1);
}

/* Viewport Info */
.test-viewport-info {
  background: rgba(253, 251, 245, 0.5);
  border: 1px solid rgba(110, 140, 101, 0.2);
  border-radius: 12px;
  padding: 16px;
  text-align: center;
}

.test-timestamp {
  font-size: 0.875rem;
  color: var(--color-heritage-green, #6E8C65);
  opacity: 0.8;
  margin: 8px 0 0 0;
}

/* Loading State */
.test-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(110, 140, 101, 0.2);
  border-top: 4px solid var(--color-heritage-green, #6E8C65);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Mobile Responsive */
@media screen and (max-width: 767px) {
  .responsive-test-panel {
    padding: 10px;
  }
  
  .responsive-test-content {
    max-height: 95vh;
  }
  
  .responsive-test-header {
    padding: 16px;
  }
  
  .responsive-test-header h2 {
    font-size: 1.25rem;
  }
  
  .responsive-test-body {
    padding: 16px;
  }
  
  .device-selector {
    grid-template-columns: 1fr;
  }
  
  .summary-stats {
    grid-template-columns: 1fr;
  }
  
  .breakdown-grid {
    grid-template-columns: 1fr;
  }
  
  .breakdown-stats {
    flex-direction: column;
    gap: 8px;
  }
}
