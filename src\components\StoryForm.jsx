import React, { useState, useEffect } from 'react';
import TagInput from './TagInput';
import SaveIndicator from './SaveIndicator';
import GuestPrompt from './GuestPrompt';
import { useAuth } from '../AuthContext';

const TOPICS = [
  'Culture & Heritage',
  'Family & Community',
  'Art & Creativity',
  'History & Legacy',
  'Education & Achievement',
  'Business & Entrepreneurship',
  'Health & Wellness',
  'Inspiration & Overcoming',
  'Activism & Leadership',
  'Joy & Celebration',
];

const TOPIC_EMOJIS = {
  'Culture & Heritage': '🏛️',
  'Family & Community': '👨‍👩‍👧‍👦',
  'Art & Creativity': '🎨',
  'History & Legacy': '📚',
  'Education & Achievement': '🎓',
  'Business & Entrepreneurship': '💼',
  'Health & Wellness': '💪',
  'Inspiration & Overcoming': '⭐',
  'Activism & Leadership': '✊',
  'Joy & Celebration': '🎉',
};

export default function StoryForm({ onSubmit, isSubmitting, onFormClear }) {
  const { currentUser, isGuestMode } = useAuth();
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [topic, setTopic] = useState(TOPICS[0]);
  const [image, setImage] = useState(null);
  const [tags, setTags] = useState([]);
  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});
  const [autoSaveStatus, setAutoSaveStatus] = useState('idle'); // 'idle', 'saving', 'saved', 'error'
  const [showSaveIndicator, setShowSaveIndicator] = useState(false);

  // Show guest prompt if user is not authenticated
  if (!currentUser && isGuestMode) {
    return (
      <GuestPrompt
        title="Share Your Story"
        message="Join NAROOP to share your experiences, wisdom, and inspiration with our community. Your voice matters!"
        icon="📝"
        className="compact"
      />
    );
  }

  // Auto-save draft to localStorage
  useEffect(() => {
    const autoSaveTimer = setTimeout(() => {
      if (title.trim() || content.trim()) {
        setAutoSaveStatus('saving');
        setShowSaveIndicator(true);

        try {
          const draftData = {
            title,
            content,
            topic,
            tags,
            timestamp: new Date().toISOString()
          };

          localStorage.setItem('naroop_story_draft', JSON.stringify(draftData));

          setTimeout(() => {
            setAutoSaveStatus('saved');
            setTimeout(() => {
              setShowSaveIndicator(false);
              setAutoSaveStatus('idle');
            }, 2000);
          }, 500);
        } catch (error) {
          console.error('Auto-save failed:', error);
          setAutoSaveStatus('error');
          setTimeout(() => {
            setShowSaveIndicator(false);
            setAutoSaveStatus('idle');
          }, 3000);
        }
      }
    }, 2000); // Auto-save after 2 seconds of inactivity

    return () => clearTimeout(autoSaveTimer);
  }, [title, content, topic, tags]);

  // Load draft on component mount
  useEffect(() => {
    try {
      const savedDraft = localStorage.getItem('naroop_story_draft');
      if (savedDraft) {
        const draftData = JSON.parse(savedDraft);
        const draftAge = new Date() - new Date(draftData.timestamp);

        // Only load draft if it's less than 24 hours old
        if (draftAge < 24 * 60 * 60 * 1000) {
          setTitle(draftData.title || '');
          setContent(draftData.content || '');
          setTopic(draftData.topic || TOPICS[0]);
          setTags(draftData.tags || []);
        }
      }
    } catch (error) {
      console.error('Failed to load draft:', error);
    }
  }, []);

  function validateForm() {
    const newErrors = {};
    
    if (!title.trim()) {
      newErrors.title = 'Title is required';
    } else if (title.trim().length < 3) {
      newErrors.title = 'Title must be at least 3 characters';
    }
    
    if (!content.trim()) {
      newErrors.content = 'Your story is required';
    } else if (content.trim().length < 10) {
      newErrors.content = 'Story must be at least 10 characters';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }

  function handleBlur(field) {
    setTouched(prev => ({ ...prev, [field]: true }));
    validateForm();
  }

  function handleImageChange(e) {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        setErrors(prev => ({ ...prev, image: 'Image must be less than 5MB' }));
        return;
      }
      setImage(file);
      setErrors(prev => ({ ...prev, image: null }));
    }
  }

  function handleSubmit(e) {
    e.preventDefault();
    setTouched({ title: true, content: true });

    // Enhanced validation before submission
    if (!validateForm()) {
      // Show specific validation errors
      const errorMessages = [];
      if (!title.trim()) errorMessages.push('Title is required');
      if (!content.trim()) errorMessages.push('Story content is required');
      if (title.trim().length < 3) errorMessages.push('Title must be at least 3 characters');
      if (content.trim().length < 10) errorMessages.push('Story must be at least 10 characters');

      console.log('Form validation failed:', errorMessages);
      return;
    }

    // Prepare story data with validation
    const storyData = {
      title: title.trim(),
      content: content.trim(),
      topic: topic || TOPICS[0],
      image: image,
      tags: tags || []
    };

    console.log('Submitting story data:', storyData);
    onSubmit(storyData);

    // Clear form and draft only after successful submission
    // Note: This will be called by the parent component after successful save
  }


  // Memoize clearForm to avoid infinite effect loop
  const clearForm = React.useCallback(() => {
    setTitle('');
    setContent('');
    setTopic(TOPICS[0]);
    setImage(null);
    setTags([]);
    setErrors({});
    setTouched({});

    // Clear saved draft
    try {
      localStorage.removeItem('naroop_story_draft');
    } catch (error) {
      console.error('Failed to clear draft:', error);
    }

    // Reset file input
    const fileInput = document.querySelector('input[type="file"]');
    if (fileInput) fileInput.value = '';
  }, []);

  // Expose clearForm function to parent component (only when onFormClear changes)
  React.useEffect(() => {
    if (onFormClear) {
      onFormClear(clearForm);
    }
  }, [onFormClear, clearForm]);

  function isFormValid() {
    return title.trim().length >= 3 && content.trim().length >= 10;
  }

  const getFieldClassName = (field, baseClass) => {
    let className = baseClass;
    if (touched[field] && errors[field]) {
      className += ' error';
    } else if (touched[field] && !errors[field]) {
      className += ' success';
    }
    return className;
  };

  return (
    <>
      <SaveIndicator
        isVisible={showSaveIndicator}
        type={autoSaveStatus}
        message={
          autoSaveStatus === 'saving' ? 'Auto-saving draft...' :
          autoSaveStatus === 'saved' ? 'Draft saved!' :
          autoSaveStatus === 'error' ? 'Failed to save draft' : ''
        }
      />

      <form className="naroop-story-form" onSubmit={handleSubmit}>
        <div className="form-header">
          <h4>✍️ Share Your Story</h4>
          <div className={`auto-save-indicator auto-save-indicator--${autoSaveStatus}`}>
            <span className="auto-save-indicator__icon">
              {autoSaveStatus === 'saving' ? '💾' :
               autoSaveStatus === 'saved' ? '✅' :
               autoSaveStatus === 'error' ? '❌' : '📝'}
            </span>
            <span className="auto-save-indicator__text">
              {autoSaveStatus === 'saving' ? 'Saving draft...' :
               autoSaveStatus === 'saved' ? 'Draft saved' :
               autoSaveStatus === 'error' ? 'Save failed' : 'Auto-save enabled'}
            </span>
          </div>
        </div>

      <div className="form-field">
        <label className="form-label" htmlFor="story-title">
          Story Title *
        </label>
        <input
          id="story-title"
          type="text"
          value={title}
          onChange={e => setTitle(e.target.value)}
          onBlur={() => handleBlur('title')}
          className={getFieldClassName('title', 'form-input')}
          placeholder="Give your story a meaningful title..."
          required
          disabled={isSubmitting}
        />
        {touched.title && errors.title && (
          <span className="form-error-message">{errors.title}</span>
        )}
        {touched.title && !errors.title && title.trim() && (
          <span className="form-success-message">Great title!</span>
        )}
      </div>

      <div className="form-field">
        <label className="form-label" htmlFor="story-topic">
          Topic Category
        </label>
        <select
          id="story-topic"
          value={topic}
          onChange={e => setTopic(e.target.value)}
          className="form-select"
          disabled={isSubmitting}
        >
          {TOPICS.map(t => (
            <option key={t} value={t}>
              {TOPIC_EMOJIS[t]} {t}
            </option>
          ))}
        </select>
      </div>

      <div className="form-field">
        <label className="form-label" htmlFor="story-content">
          ✍️ Your Story *
        </label>
        <div className="form-input-wrapper">
          <textarea
            id="story-content"
            value={content}
            onChange={e => setContent(e.target.value)}
            onBlur={() => handleBlur('content')}
            className={getFieldClassName('content', 'form-textarea')}
            placeholder="✨ What's your story? Share your experience, wisdom, or inspiration..."
            required
            rows={5}
            disabled={isSubmitting}
          />
          {!content && (
            <div className="form-placeholder-icon">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
                <path d="M12 20h9"></path>
                <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path>
              </svg>
            </div>
          )}
        </div>
        <div className="character-count">
          {content.length}/2000 characters
        </div>
        {touched.content && errors.content && (
          <span className="form-error-message">{errors.content}</span>
        )}
        {touched.content && !errors.content && content.trim() && (
          <span className="form-success-message">Beautiful story!</span>
        )}
      </div>

      <div className="form-field">
        <label className="form-label" htmlFor="story-tags">
          🏷️ Tags (Optional)
        </label>
        <TagInput
          tags={tags}
          onChange={setTags}
          placeholder="Add relevant tags (press Enter or comma)..."
        />
        <div className="form-helper-text">Help others discover your story with relevant tags</div>
      </div>

      <div className="form-field">
        <label className="form-label" htmlFor="story-image">
          📷 Add an Image (Optional)
        </label>
        <input
          id="story-image"
          type="file"
          accept="image/*"
          onChange={handleImageChange}
          disabled={isSubmitting}
        />
        <div className="form-helper-text">Maximum file size: 5MB</div>
        {errors.image && (
          <span className="form-error-message">{errors.image}</span>
        )}
        {image && !errors.image && (
          <span className="form-success-message">Image ready to upload!</span>
        )}
      </div>

      <button
        type="submit"
        disabled={isSubmitting || !isFormValid()}
        className={isSubmitting ? 'loading' : ''}
        aria-label={isSubmitting ? 'Sharing your story, please wait' : 'Share your story with the community'}
      >
        {isSubmitting ? (
          <>
            <span className="loading-spinner"></span>
            Sharing Your Story...
          </>
        ) : (
          '✨ Share My Story'
        )}
      </button>
    </form>
    </>
  );
}
