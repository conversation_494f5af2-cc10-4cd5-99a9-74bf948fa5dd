import React, { useState, useEffect } from 'react';
import { 
  testAllInteractiveElements, 
  generateResponsiveReport, 
  BREAKPOINTS, 
  DEVICE_DIMENSIONS 
} from '../../utils/responsiveTestUtils';
import './ResponsiveTestPanel.css';

const ResponsiveTestPanel = ({ isVisible = false, onClose }) => {
  const [testResults, setTestResults] = useState(null);
  const [isRunning, setIsRunning] = useState(false);
  const [selectedDevice, setSelectedDevice] = useState('iPhone12');

  useEffect(() => {
    if (isVisible) {
      runTests();
    }
  }, [isVisible]);

  const runTests = async () => {
    setIsRunning(true);
    try {
      // Wait a moment for any animations to settle
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const report = generateResponsiveReport();
      setTestResults(report);
    } catch (error) {
      console.error('Error running responsive tests:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const simulateDevice = (deviceKey) => {
    const device = DEVICE_DIMENSIONS[deviceKey];
    if (device) {
      // Note: In a real implementation, you'd use browser dev tools or a testing framework
      // This is a simplified simulation for demonstration
      document.documentElement.style.setProperty('--test-viewport-width', `${device.width}px`);
      document.documentElement.style.setProperty('--test-viewport-height', `${device.height}px`);
      setSelectedDevice(deviceKey);
      
      // Re-run tests after device simulation
      setTimeout(runTests, 300);
    }
  };

  const getScoreColor = (score) => {
    if (score >= 90) return '#6E8C65'; // NAROOP Green
    if (score >= 70) return '#F7D046'; // NAROOP Yellow
    return '#591C28'; // NAROOP Maroon
  };

  const getScoreLabel = (score) => {
    if (score >= 90) return 'Excellent';
    if (score >= 80) return 'Good';
    if (score >= 70) return 'Fair';
    return 'Needs Improvement';
  };

  if (!isVisible) return null;

  return (
    <div className="responsive-test-panel">
      <div className="responsive-test-overlay" onClick={onClose} />
      <div className="responsive-test-content">
        <div className="responsive-test-header">
          <h2>NAROOP Responsive Design Test</h2>
          <button 
            className="responsive-test-close" 
            onClick={onClose}
            aria-label="Close test panel"
          >
            ×
          </button>
        </div>

        <div className="responsive-test-body">
          {/* Device Simulation */}
          <div className="test-section">
            <h3>Device Simulation</h3>
            <div className="device-selector">
              {Object.entries(DEVICE_DIMENSIONS).map(([key, device]) => (
                <button
                  key={key}
                  className={`device-btn ${selectedDevice === key ? 'device-btn--active' : ''}`}
                  onClick={() => simulateDevice(key)}
                >
                  {device.name}
                  <span className="device-dimensions">
                    {device.width}×{device.height}
                  </span>
                </button>
              ))}
            </div>
          </div>

          {/* Test Controls */}
          <div className="test-section">
            <h3>Test Controls</h3>
            <button 
              className="test-run-btn"
              onClick={runTests}
              disabled={isRunning}
            >
              {isRunning ? 'Running Tests...' : 'Run Responsive Tests'}
            </button>
          </div>

          {/* Test Results */}
          {testResults && (
            <div className="test-section">
              <h3>Test Results</h3>
              
              {/* Overall Score */}
              <div className="test-score">
                <div 
                  className="score-circle"
                  style={{ borderColor: getScoreColor(testResults.score) }}
                >
                  <span className="score-number">{testResults.score}</span>
                  <span className="score-label">{getScoreLabel(testResults.score)}</span>
                </div>
              </div>

              {/* Interactive Elements Summary */}
              <div className="test-summary">
                <h4>Interactive Elements</h4>
                <div className="summary-stats">
                  <div className="stat">
                    <span className="stat-label">Total Elements:</span>
                    <span className="stat-value">{testResults.interactiveElements.totalElements}</span>
                  </div>
                  <div className="stat">
                    <span className="stat-label">Passed:</span>
                    <span className="stat-value stat-value--success">
                      {testResults.interactiveElements.passedElements}
                    </span>
                  </div>
                  <div className="stat">
                    <span className="stat-label">Failed:</span>
                    <span className="stat-value stat-value--error">
                      {testResults.interactiveElements.failedElements}
                    </span>
                  </div>
                </div>
              </div>

              {/* Detailed Breakdown */}
              <div className="test-breakdown">
                <h4>Detailed Breakdown</h4>
                <div className="breakdown-grid">
                  <div className="breakdown-item">
                    <h5>Touch Targets</h5>
                    <div className="breakdown-stats">
                      <span className="breakdown-passed">
                        ✓ {testResults.interactiveElements.summary.touchTargets.passed}
                      </span>
                      <span className="breakdown-failed">
                        ✗ {testResults.interactiveElements.summary.touchTargets.failed}
                      </span>
                    </div>
                  </div>
                  <div className="breakdown-item">
                    <h5>Accessibility</h5>
                    <div className="breakdown-stats">
                      <span className="breakdown-passed">
                        ✓ {testResults.interactiveElements.summary.accessibility.passed}
                      </span>
                      <span className="breakdown-failed">
                        ✗ {testResults.interactiveElements.summary.accessibility.failed}
                      </span>
                    </div>
                  </div>
                  <div className="breakdown-item">
                    <h5>Spacing</h5>
                    <div className="breakdown-stats">
                      <span className="breakdown-passed">
                        ✓ {testResults.interactiveElements.summary.spacing.passed}
                      </span>
                      <span className="breakdown-failed">
                        ✗ {testResults.interactiveElements.summary.spacing.failed}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Recommendations */}
              {testResults.recommendations && testResults.recommendations.length > 0 && (
                <div className="test-recommendations">
                  <h4>Recommendations</h4>
                  <ul className="recommendations-list">
                    {testResults.recommendations.map((rec, index) => (
                      <li 
                        key={index} 
                        className={`recommendation recommendation--${rec.priority}`}
                      >
                        <strong>{rec.category}:</strong> {rec.message}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Viewport Info */}
              <div className="test-viewport-info">
                <h4>Current Viewport</h4>
                <p>
                  {testResults.viewport.width} × {testResults.viewport.height} pixels
                </p>
                <p className="test-timestamp">
                  Tested: {new Date(testResults.timestamp).toLocaleString()}
                </p>
              </div>
            </div>
          )}

          {/* Loading State */}
          {isRunning && (
            <div className="test-loading">
              <div className="loading-spinner"></div>
              <p>Running responsive design tests...</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ResponsiveTestPanel;
