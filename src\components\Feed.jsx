import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, Compass, Users, Bell, MessageSquare, Heart, MessageCircle, Repeat2, Bookmark, Plus, MoreVertical } from 'lucide-react';

const Feed = () => {
    return (
        <div className="bg-naroop-background text-naroop-text-primary min-h-screen font-sans">
            <div className="container mx-auto grid grid-cols-1 lg:grid-cols-12 gap-8 py-8">

                {/* Left Column */}
                <aside className="lg:col-span-3 space-y-6">
                    <div className="bg-naroop-surface p-6 rounded-2xl shadow-sm">
                        <div className="flex flex-col items-center text-center">
                            <div className="w-24 h-24 rounded-full bg-naroop-primary flex items-center justify-center mb-4">
                                {/* Placeholder for silhouette icon */}
                                <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 24 24" fill="none" stroke="#1F2937" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>
                            </div>
                            <h2 className="text-2xl font-bold text-naroop-text-primary">John D</h2>
                            <p className="text-sm text-naroop-text-secondary mt-2">As a black man who strides for always trying to do something great.</p>
                        </div>
                        <div className="grid grid-cols-3 gap-4 text-center my-6">
                            <div>
                                <p className="font-bold text-xl">1</p>
                                <p className="text-xs text-naroop-text-secondary">Stories</p>
                            </div>
                            <div>
                                <p className="font-bold text-xl">0</p>
                                <p className="text-xs text-naroop-text-secondary">Followers</p>
                            </div>
                            <div>
                                <p className="font-bold text-xl">0</p>
                                <p className="text-xs text-naroop-text-secondary">Following</p>
                            </div>
                        </div>
                        <div className="flex flex-wrap gap-2 justify-center">
                            <span className="bg-naroop-primary/20 text-naroop-text-primary text-xs font-semibold px-3 py-1.5 rounded-full">Storytelling</span>
                            <span className="bg-naroop-primary/20 text-naroop-text-primary text-xs font-semibold px-3 py-1.5 rounded-full">Community</span>
                            <span className="bg-naroop-primary/20 text-naroop-text-primary text-xs font-semibold px-3 py-1.5 rounded-full">Leadership</span>
                        </div>
                    </div>
                    <div className="bg-naroop-surface p-6 rounded-2xl shadow-sm text-center">
                         <button className="w-full bg-transparent border border-naroop-secondary text-naroop-secondary font-semibold py-3 rounded-full hover:bg-naroop-secondary/10 transition-colors duration-300">My Profile</button>
                    </div>
                </aside>

                {/* Center Column */}
                <main className="lg:col-span-6 space-y-6">
                    <div>
                        <h2 className="text-xl font-bold mb-4 flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="#FBBF24" stroke="#1F2937" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87L18.18 22 12 18.77 5.82 22 7 14.14 2 9.27l6.91-1.01L12 2z"></path></svg> Featured Stories</h2>
                        <div className="bg-naroop-surface p-5 rounded-2xl shadow-sm flex items-center gap-4">
                             <div className="w-12 h-12 rounded-full bg-naroop-primary flex items-center justify-center flex-shrink-0">
                                <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="#1F2937" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>
                            </div>
                            <div>
                                <p className="text-sm text-naroop-text-secondary">Personal Experience</p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-naroop-dark-surface p-4 rounded-2xl shadow-sm flex items-center gap-4">
                        <div className="w-10 h-10 rounded-full bg-naroop-primary flex items-center justify-center flex-shrink-0">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#1F2937" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>
                        </div>
                        <input type="text" placeholder="Share your story..." className="w-full bg-transparent text-naroop-dark-text placeholder-naroop-text-secondary/70 focus:outline-none" />
                    </div>

                    <div>
                        <h2 className="text-xl font-bold mb-4">Recent Stories</h2>
                        <article className="bg-naroop-dark-surface p-5 rounded-2xl shadow-sm text-naroop-dark-text">
                            <div className="flex items-center mb-4">
                                <div className="w-12 h-12 rounded-full bg-naroop-primary flex items-center justify-center flex-shrink-0">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="#1F2937" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>
                                </div>
                                <div className="ml-4 flex-grow">
                                    <h3 className="font-bold">John D</h3>
                                    <p className="text-sm text-naroop-text-secondary">Just now</p>
                                </div>
                                <span className="bg-naroop-primary/80 text-naroop-text-primary text-xs font-semibold px-3 py-1.5 rounded-full">Personal Experience</span>
                            </div>
                            <h2 className="text-2xl font-bold mb-2">Test story</h2>
                            <p className="text-naroop-text-secondary leading-relaxed">Test story</p>
                            <div className="mt-4 pt-4 border-t border-naroop-text-secondary/20 flex items-center gap-6">
                                <button className="flex items-center gap-2 text-naroop-text-secondary hover:text-red-500 font-medium transition-colors">
                                    <Heart className="w-5 h-5" /> 0
                                </button>
                                 <button className="flex items-center gap-2 text-naroop-text-secondary hover:text-sky-500 font-medium transition-colors">
                                    <MessageCircle className="w-5 h-5" /> 0
                                </button>
                                 <button className="flex items-center gap-2 text-naroop-text-secondary hover:text-emerald-500 font-medium transition-colors">
                                    <Repeat2 className="w-5 h-5" /> 0
                                </button>
                                 <button className="flex items-center gap-2 text-naroop-text-secondary hover:text-naroop-primary font-medium transition-colors ml-auto">
                                    <Bookmark className="w-5 h-5" />
                                </button>
                            </div>
                        </article>
                    </div>
                </main>

                {/* Right Column */}
                <aside className="lg:col-span-3 space-y-6">
                    <div className="bg-naroop-dark-surface p-6 rounded-2xl shadow-sm text-naroop-dark-text">
                        <h3 className="font-bold text-lg mb-4">Quick Actions</h3>
                        <div className="space-y-3">
                            <button className="w-full flex items-center gap-3 text-left p-3 rounded-lg border-2 border-naroop-text-secondary/30 hover:bg-naroop-text-secondary/10 transition-colors">
                                <PenSquare className="w-5 h-5 text-naroop-primary"/> Write
                            </button>
                            <button className="w-full flex items-center gap-3 text-left p-3 rounded-lg border-2 border-naroop-text-secondary/30 hover:bg-naroop-text-secondary/10 transition-colors">
                                <Compass className="w-5 h-5 text-naroop-primary"/> Explore
                            </button>
                            <button className="w-full flex items-center gap-3 text-left p-3 rounded-lg border-2 border-naroop-text-secondary/30 hover:bg-naroop-text-secondary/10 transition-colors">
                                <Users className="w-5 h-5 text-naroop-primary"/> Connect
                            </button>
                        </div>
                    </div>

                    <div className="bg-naroop-dark-surface p-6 rounded-2xl shadow-sm text-naroop-dark-text">
                        <div className="flex justify-between items-center mb-4">
                            <h3 className="font-bold text-lg">Notifications</h3>
                            <span className="bg-naroop-primary text-naroop-text-primary text-xs font-bold w-6 h-6 flex items-center justify-center rounded-full">2</span>
                        </div>
                        <ul className="space-y-4">
                            <li className="flex items-start gap-3">
                                <div className="w-5 h-5 mt-1 flex-shrink-0 text-red-500"><Heart/></div>
                                <p className="text-sm"><span className="font-semibold">Maya</span> loved your story "My Journey". <span className="text-naroop-text-secondary block">2h ago</span></p>
                            </li>
                            <li className="flex items-start gap-3">
                                <div className="w-5 h-5 mt-1 flex-shrink-0 text-sky-500"><MessageCircle/></div>
                                <p className="text-sm">New comment on "Community Building". <span className="text-naroop-text-secondary block">4h ago</span></p>
                            </li>
                             <li className="flex items-start gap-3">
                                <div className="w-5 h-5 mt-1 flex-shrink-0 text-naroop-primary"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87L18.18 22 12 18.77 5.82 22 7 14.14 2 9.27l6.91-1.01L12 2z"></path></svg></div>
                                <p className="text-sm">You reached 50 hearts! <span className="text-naroop-text-secondary block">1d ago</span></p>
                            </li>
                        </ul>
                    </div>

                     <div className="bg-naroop-dark-surface p-6 rounded-2xl shadow-sm text-naroop-dark-text">
                        <h3 className="font-bold text-lg mb-4">Recent Activity</h3>
                        <ul className="space-y-4">
                            <li className="flex items-start gap-3">
                                <div className="w-10 h-10 rounded-full bg-gray-500 flex-shrink-0"></div>
                                <p className="text-sm">New story from <span className="font-semibold">Marcus</span>. <span className="text-naroop-text-secondary block">1h ago</span></p>
                            </li>
                             <li className="flex items-start gap-3">
                                <div className="w-10 h-10 rounded-full bg-gray-500 flex-shrink-0"></div>
                                <p className="text-sm"><span className="font-semibold">Alicia</span> earned "Storyteller" badge. <span className="text-naroop-text-secondary block">3h ago</span></p>
                            </li>
                             <li className="flex items-start gap-3">
                                <div className="w-10 h-10 rounded-full bg-gray-500 flex-shrink-0"></div>
                                <p className="text-sm"><span className="font-semibold">Sarah</span> joined Creative Arts. <span className="text-naroop-text-secondary block">5h ago</span></p>
                            </li>
                        </ul>
                    </div>

                    <div className="bg-naroop-dark-surface p-6 rounded-2xl shadow-sm text-naroop-dark-text">
                        <h3 className="font-bold text-lg mb-4">People You May Know</h3>
                        <div className="space-y-4">
                            <div className="flex items-center gap-3">
                                <div className="w-10 h-10 rounded-full bg-gray-500 flex-shrink-0"></div>
                                <div className="flex-grow">
                                    <p className="font-semibold">Dr. Angela Brown</p>
                                    <p className="text-sm text-naroop-text-secondary">Educator</p>
                                </div>
                                <button className="bg-transparent border border-naroop-secondary text-naroop-secondary font-semibold py-2 px-4 rounded-full hover:bg-naroop-secondary/10 transition-colors duration-300 text-sm">Connect</button>
                            </div>
                             <div className="flex items-center gap-3">
                                <div className="w-10 h-10 rounded-full bg-gray-500 flex-shrink-0"></div>
                                <div className="flex-grow">
                                    <p className="font-semibold">James Wilson</p>
                                    <p className="text-sm text-naroop-text-secondary">Designer</p>
                                </div>
                                <button className="bg-transparent border border-naroop-secondary text-naroop-secondary font-semibold py-2 px-4 rounded-full hover:bg-naroop-secondary/10 transition-colors duration-300 text-sm">Connect</button>
                            </div>
                        </div>
                    </div>
                </aside>

            </div>
        </div>
    );
};

export default Feed;
