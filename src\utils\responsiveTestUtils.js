/**
 * NAROOP Responsive Design Testing Utilities
 * Comprehensive testing for mobile-first responsive design
 */

// Responsive breakpoints for testing
export const BREAKPOINTS = {
  mobile: {
    min: 320,
    max: 767,
    name: 'Mobile'
  },
  tablet: {
    min: 768,
    max: 1023,
    name: 'Tablet'
  },
  desktop: {
    min: 1024,
    max: 1439,
    name: 'Desktop'
  },
  largeDesktop: {
    min: 1440,
    max: 1920,
    name: 'Large Desktop'
  }
};

// Common mobile device dimensions for testing
export const DEVICE_DIMENSIONS = {
  iPhoneSE: { width: 375, height: 667, name: 'iPhone SE' },
  iPhone12: { width: 390, height: 844, name: 'iPhone 12' },
  iPhone12Pro: { width: 393, height: 852, name: 'iPhone 12 Pro' },
  iPhone14ProMax: { width: 430, height: 932, name: 'iPhone 14 Pro Max' },
  galaxyS21: { width: 384, height: 854, name: 'Galaxy S21' },
  galaxyS21Ultra: { width: 412, height: 915, name: 'Galaxy S21 Ultra' },
  iPadMini: { width: 768, height: 1024, name: 'iPad Mini' },
  iPadAir: { width: 820, height: 1180, name: 'iPad Air' },
  iPadPro: { width: 1024, height: 1366, name: 'iPad Pro' }
};

// Touch target requirements
export const TOUCH_REQUIREMENTS = {
  minSize: 44, // Minimum touch target size in pixels
  comfortableSize: 48, // Comfortable touch target size
  spacing: 8 // Minimum spacing between touch targets
};

// WCAG AA accessibility requirements
export const ACCESSIBILITY_REQUIREMENTS = {
  contrastRatio: {
    normal: 4.5,
    large: 3.0
  },
  fontSize: {
    minimum: 16, // Prevents zoom on iOS
    comfortable: 18
  }
};

/**
 * Test if an element meets touch target requirements
 * @param {HTMLElement} element - The element to test
 * @returns {Object} Test results
 */
export function testTouchTarget(element) {
  const rect = element.getBoundingClientRect();
  const computedStyle = window.getComputedStyle(element);
  
  const width = rect.width;
  const height = rect.height;
  const minSize = TOUCH_REQUIREMENTS.minSize;
  
  return {
    element: element,
    width: width,
    height: height,
    meetsMinimum: width >= minSize && height >= minSize,
    meetsComfortable: width >= TOUCH_REQUIREMENTS.comfortableSize && height >= TOUCH_REQUIREMENTS.comfortableSize,
    isInteractive: element.matches('button, [role="button"], a, input, select, textarea, [tabindex]'),
    hasProperSpacing: checkTouchTargetSpacing(element),
    recommendations: generateTouchTargetRecommendations(width, height, minSize)
  };
}

/**
 * Check spacing between touch targets
 * @param {HTMLElement} element - The element to check
 * @returns {boolean} Whether spacing is adequate
 */
function checkTouchTargetSpacing(element) {
  const rect = element.getBoundingClientRect();
  const siblings = Array.from(element.parentElement?.children || [])
    .filter(child => child !== element && child.matches('button, [role="button"], a, input, select, textarea, [tabindex]'));
  
  return siblings.every(sibling => {
    const siblingRect = sibling.getBoundingClientRect();
    const distance = Math.min(
      Math.abs(rect.left - siblingRect.right),
      Math.abs(rect.right - siblingRect.left),
      Math.abs(rect.top - siblingRect.bottom),
      Math.abs(rect.bottom - siblingRect.top)
    );
    return distance >= TOUCH_REQUIREMENTS.spacing;
  });
}

/**
 * Generate recommendations for touch target improvements
 * @param {number} width - Current width
 * @param {number} height - Current height
 * @param {number} minSize - Minimum required size
 * @returns {Array} Array of recommendations
 */
function generateTouchTargetRecommendations(width, height, minSize) {
  const recommendations = [];
  
  if (width < minSize) {
    recommendations.push(`Increase width from ${width}px to at least ${minSize}px`);
  }
  
  if (height < minSize) {
    recommendations.push(`Increase height from ${height}px to at least ${minSize}px`);
  }
  
  if (width >= minSize && height >= minSize && (width < TOUCH_REQUIREMENTS.comfortableSize || height < TOUCH_REQUIREMENTS.comfortableSize)) {
    recommendations.push(`Consider increasing to ${TOUCH_REQUIREMENTS.comfortableSize}px for better usability`);
  }
  
  return recommendations;
}

/**
 * Test responsive layout at different breakpoints
 * @param {string} selector - CSS selector for elements to test
 * @returns {Object} Test results for each breakpoint
 */
export function testResponsiveLayout(selector = 'body') {
  const results = {};
  const originalWidth = window.innerWidth;
  const originalHeight = window.innerHeight;
  
  Object.entries(BREAKPOINTS).forEach(([key, breakpoint]) => {
    // Test at minimum width of breakpoint
    window.resizeTo(breakpoint.min, 800);
    
    // Wait for layout to settle
    setTimeout(() => {
      const elements = document.querySelectorAll(selector);
      results[key] = {
        breakpoint: breakpoint,
        width: breakpoint.min,
        elements: Array.from(elements).map(el => ({
          selector: getElementSelector(el),
          visible: isElementVisible(el),
          overflowing: isElementOverflowing(el),
          touchTargets: el.querySelectorAll('button, [role="button"], a, input, select, textarea, [tabindex]').length
        }))
      };
    }, 100);
  });
  
  // Restore original size
  setTimeout(() => {
    window.resizeTo(originalWidth, originalHeight);
  }, 500);
  
  return results;
}

/**
 * Check if element is visible
 * @param {HTMLElement} element - Element to check
 * @returns {boolean} Whether element is visible
 */
function isElementVisible(element) {
  const style = window.getComputedStyle(element);
  return style.display !== 'none' && 
         style.visibility !== 'hidden' && 
         style.opacity !== '0' &&
         element.offsetWidth > 0 && 
         element.offsetHeight > 0;
}

/**
 * Check if element is overflowing its container
 * @param {HTMLElement} element - Element to check
 * @returns {boolean} Whether element is overflowing
 */
function isElementOverflowing(element) {
  return element.scrollWidth > element.clientWidth || 
         element.scrollHeight > element.clientHeight;
}

/**
 * Get a unique selector for an element
 * @param {HTMLElement} element - Element to get selector for
 * @returns {string} CSS selector
 */
function getElementSelector(element) {
  if (element.id) return `#${element.id}`;
  if (element.className) return `.${element.className.split(' ')[0]}`;
  return element.tagName.toLowerCase();
}

/**
 * Test all interactive elements on the page
 * @returns {Object} Comprehensive test results
 */
export function testAllInteractiveElements() {
  const interactiveElements = document.querySelectorAll(
    'button, [role="button"], a, input, select, textarea, [tabindex]:not([tabindex="-1"])'
  );
  
  const results = {
    totalElements: interactiveElements.length,
    passedElements: 0,
    failedElements: 0,
    elements: [],
    summary: {
      touchTargets: { passed: 0, failed: 0 },
      accessibility: { passed: 0, failed: 0 },
      spacing: { passed: 0, failed: 0 }
    }
  };
  
  interactiveElements.forEach(element => {
    const touchTest = testTouchTarget(element);
    const accessibilityTest = testElementAccessibility(element);
    
    const elementResult = {
      element: element,
      selector: getElementSelector(element),
      touchTarget: touchTest,
      accessibility: accessibilityTest,
      passed: touchTest.meetsMinimum && accessibilityTest.passed
    };
    
    results.elements.push(elementResult);
    
    if (elementResult.passed) {
      results.passedElements++;
    } else {
      results.failedElements++;
    }
    
    // Update summary
    if (touchTest.meetsMinimum) results.summary.touchTargets.passed++;
    else results.summary.touchTargets.failed++;
    
    if (accessibilityTest.passed) results.summary.accessibility.passed++;
    else results.summary.accessibility.failed++;
    
    if (touchTest.hasProperSpacing) results.summary.spacing.passed++;
    else results.summary.spacing.failed++;
  });
  
  return results;
}

/**
 * Test element accessibility
 * @param {HTMLElement} element - Element to test
 * @returns {Object} Accessibility test results
 */
function testElementAccessibility(element) {
  const computedStyle = window.getComputedStyle(element);
  const fontSize = parseFloat(computedStyle.fontSize);
  
  return {
    fontSize: fontSize,
    meetsMinimumFontSize: fontSize >= ACCESSIBILITY_REQUIREMENTS.fontSize.minimum,
    hasAccessibleName: element.hasAttribute('aria-label') || 
                      element.hasAttribute('aria-labelledby') || 
                      element.textContent.trim().length > 0,
    hasProperRole: element.hasAttribute('role') || 
                   ['button', 'a', 'input', 'select', 'textarea'].includes(element.tagName.toLowerCase()),
    passed: fontSize >= ACCESSIBILITY_REQUIREMENTS.fontSize.minimum &&
            (element.hasAttribute('aria-label') || 
             element.hasAttribute('aria-labelledby') || 
             element.textContent.trim().length > 0)
  };
}

/**
 * Generate a comprehensive responsive design report
 * @returns {Object} Complete test report
 */
export function generateResponsiveReport() {
  const interactiveTest = testAllInteractiveElements();
  const layoutTest = testResponsiveLayout();
  
  return {
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent,
    viewport: {
      width: window.innerWidth,
      height: window.innerHeight
    },
    interactiveElements: interactiveTest,
    responsiveLayout: layoutTest,
    recommendations: generateRecommendations(interactiveTest, layoutTest),
    score: calculateResponsiveScore(interactiveTest, layoutTest)
  };
}

/**
 * Generate recommendations based on test results
 * @param {Object} interactiveTest - Interactive elements test results
 * @param {Object} layoutTest - Layout test results
 * @returns {Array} Array of recommendations
 */
function generateRecommendations(interactiveTest, layoutTest) {
  const recommendations = [];
  
  if (interactiveTest.summary.touchTargets.failed > 0) {
    recommendations.push({
      priority: 'high',
      category: 'Touch Targets',
      message: `${interactiveTest.summary.touchTargets.failed} elements don't meet minimum touch target size of 44px`
    });
  }
  
  if (interactiveTest.summary.accessibility.failed > 0) {
    recommendations.push({
      priority: 'high',
      category: 'Accessibility',
      message: `${interactiveTest.summary.accessibility.failed} elements have accessibility issues`
    });
  }
  
  if (interactiveTest.summary.spacing.failed > 0) {
    recommendations.push({
      priority: 'medium',
      category: 'Spacing',
      message: `${interactiveTest.summary.spacing.failed} elements don't have adequate spacing`
    });
  }
  
  return recommendations;
}

/**
 * Calculate overall responsive design score
 * @param {Object} interactiveTest - Interactive elements test results
 * @param {Object} layoutTest - Layout test results
 * @returns {number} Score from 0-100
 */
function calculateResponsiveScore(interactiveTest, layoutTest) {
  const touchScore = (interactiveTest.summary.touchTargets.passed / interactiveTest.totalElements) * 40;
  const accessibilityScore = (interactiveTest.summary.accessibility.passed / interactiveTest.totalElements) * 40;
  const spacingScore = (interactiveTest.summary.spacing.passed / interactiveTest.totalElements) * 20;
  
  return Math.round(touchScore + accessibilityScore + spacingScore);
}
