/* NAROOP Mobile Navigation - Light Theme */

.mobile-navigation {
  position: relative;
  z-index: 1000;
}

/* ===== HAMBURGER MENU BUTTON ===== */
.mobile-nav-hamburger {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1100;
  background: var(--color-heritage-maroon, #591C28);
  border: 3px solid var(--color-heritage-yellow, #F7D046);
  border-radius: 16px;
  width: 56px;
  height: 56px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 6px 20px rgba(89, 28, 40, 0.4);
  transform: scale(1);
}

.mobile-nav-hamburger:hover {
  background: var(--color-heritage-yellow, #F7D046);
  border-color: var(--color-heritage-maroon, #591C28);
  transform: scale(1.1);
  box-shadow: 0 8px 24px rgba(89, 28, 40, 0.5);
  animation: pulse 0.6s ease-in-out;
}

.mobile-nav-hamburger:active {
  transform: scale(0.95);
  box-shadow: 0 4px 12px rgba(89, 28, 40, 0.6);
}

.mobile-nav-hamburger__line {
  width: 28px;
  height: 4px;
  background: var(--color-heritage-cream, #FDFBF5);
  border-radius: 3px;
  transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  transform-origin: center;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.mobile-nav-hamburger--active {
  background: var(--color-heritage-yellow, #F7D046);
  border-color: var(--color-heritage-maroon, #591C28);
  transform: rotate(180deg) scale(1.05);
  animation: menuOpen 0.5s ease-out;
}

.mobile-nav-hamburger--active .mobile-nav-hamburger__line {
  background: var(--color-heritage-maroon, #591C28);
}

.mobile-nav-hamburger--active .mobile-nav-hamburger__line:nth-child(1) {
  transform: rotate(45deg) translate(7px, 7px);
}

.mobile-nav-hamburger--active .mobile-nav-hamburger__line:nth-child(2) {
  opacity: 0;
  transform: scale(0);
}

.mobile-nav-hamburger--active .mobile-nav-hamburger__line:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -7px);
}

/* Hamburger Menu Animations */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.15); }
  100% { transform: scale(1.1); }
}

@keyframes menuOpen {
  0% { transform: rotate(0deg) scale(1); }
  50% { transform: rotate(90deg) scale(1.1); }
  100% { transform: rotate(180deg) scale(1.05); }
}

/* ===== HAMBURGER MENU OVERLAY ===== */
.mobile-nav-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(89, 28, 40, 0.5);
  z-index: 1050;
  backdrop-filter: blur(4px);
}

/* ===== HAMBURGER MENU CONTENT ===== */
.mobile-nav-hamburger-menu {
  position: fixed;
  top: 0;
  right: 0;
  width: 320px;
  max-width: 85vw;
  height: 100vh;
  background: var(--color-heritage-cream, #FDFBF5);
  border-left: 2px solid var(--color-heritage-green, #6E8C65);
  z-index: 1060;
  overflow-y: auto;
  animation: slideInRight 0.3s ease-out;
  box-shadow: -4px 0 20px rgba(89, 28, 40, 0.15);
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

.mobile-nav-hamburger-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid rgba(110, 140, 101, 0.2);
  background: linear-gradient(135deg, var(--color-heritage-yellow, #F7D046) 0%, var(--color-heritage-green, #6E8C65) 100%);
}

.mobile-nav-hamburger-header h3 {
  margin: 0;
  color: var(--color-heritage-maroon, #591C28);
  font-size: 1.25rem;
  font-weight: 700;
}

.mobile-nav-close {
  background: transparent;
  border: none;
  color: var(--color-heritage-maroon, #591C28);
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mobile-nav-close:hover {
  background: rgba(89, 28, 40, 0.1);
}

.mobile-nav-close svg {
  width: 20px;
  height: 20px;
}

.mobile-nav-hamburger-content {
  padding: 20px;
}

.mobile-nav-section {
  margin-bottom: 32px;
}

.mobile-nav-section h4 {
  margin: 0 0 16px 0;
  color: var(--color-heritage-green, #6E8C65);
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.mobile-nav-hamburger-item {
  width: 100%;
  background: transparent;
  border: none;
  padding: 16px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 8px;
  text-align: left;
  min-height: 64px; /* Touch-friendly */
}

.mobile-nav-hamburger-item:hover {
  background: rgba(110, 140, 101, 0.1);
  transform: translateX(4px);
}

.mobile-nav-hamburger-item--active {
  background: linear-gradient(135deg, var(--color-heritage-yellow, #F7D046) 0%, rgba(247, 208, 70, 0.2) 100%);
  border: 1px solid var(--color-heritage-yellow, #F7D046);
}

.mobile-nav-hamburger-icon {
  width: 24px;
  height: 24px;
  color: var(--color-heritage-maroon, #591C28);
  flex-shrink: 0;
}

.mobile-nav-hamburger-text {
  flex: 1;
}

.mobile-nav-hamburger-label {
  display: block;
  color: var(--color-heritage-maroon, #591C28);
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 4px;
}

.mobile-nav-hamburger-description {
  display: block;
  color: var(--color-heritage-green, #6E8C65);
  font-size: 0.875rem;
  opacity: 0.8;
}

/* ===== BOTTOM TAB BAR ===== */
.mobile-nav-bottom-tabs {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--color-heritage-cream, #FDFBF5);
  border-top: 2px solid var(--color-heritage-yellow, #F7D046);
  padding: 8px 0 calc(8px + env(safe-area-inset-bottom));
  z-index: 1000;
  display: flex;
  justify-content: space-around;
  align-items: center;
  box-shadow: 0 -4px 12px rgba(89, 28, 40, 0.1);
}

.mobile-nav-tab {
  background: transparent;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 8px 4px;
  border-radius: 12px;
  min-width: 48px;
  min-height: 48px; /* Touch-friendly */
  flex: 1;
  max-width: 80px;
}

.mobile-nav-tab:hover {
  background: rgba(110, 140, 101, 0.1);
  transform: translateY(-2px);
}

.mobile-nav-tab--active {
  background: var(--color-heritage-maroon, #591C28);
  color: var(--color-heritage-cream, #FDFBF5);
  transform: translateY(-4px);
  box-shadow: 0 6px 16px rgba(89, 28, 40, 0.3);
  border: 2px solid var(--color-heritage-yellow, #F7D046);
  position: relative;
}

.mobile-nav-tab--active::before {
  content: '';
  position: absolute;
  top: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background: var(--color-heritage-yellow, #F7D046);
  border-radius: 50%;
}

.mobile-nav-tab-icon {
  width: 24px;
  height: 24px;
  color: var(--color-heritage-maroon, #591C28);
  transition: all 0.3s ease;
}

.mobile-nav-tab--active .mobile-nav-tab-icon {
  color: var(--color-heritage-cream, #FDFBF5);
  transform: scale(1.1);
}

.mobile-nav-tab-label {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--color-heritage-green, #6E8C65);
  text-align: center;
  line-height: 1.2;
}

.mobile-nav-tab--active .mobile-nav-tab-label {
  color: var(--color-heritage-cream, #FDFBF5);
  font-weight: 600;
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */
@media screen and (max-width: 480px) {
  .mobile-nav-hamburger-menu {
    width: 100vw;
    max-width: 100vw;
  }
  
  .mobile-nav-tab-label {
    font-size: 0.6875rem;
  }
  
  .mobile-nav-tab {
    min-width: 40px;
    padding: 6px 2px;
  }
  
  .mobile-nav-tab-icon {
    width: 20px;
    height: 20px;
  }
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  .mobile-nav-hamburger,
  .mobile-nav-hamburger__line,
  .mobile-nav-hamburger-item,
  .mobile-nav-tab {
    transition: none;
  }
  
  .mobile-nav-hamburger-menu {
    animation: none;
  }
}

/* Hide on desktop */
@media screen and (min-width: 768px) {
  .mobile-navigation {
    display: none;
  }
}
