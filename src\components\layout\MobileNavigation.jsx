import React, { useState, useEffect } from 'react';
import './MobileNavigation.css';

const MobileNavigation = ({
  currentSection = 'home',
  onNavigate,
  userRole,
  ADMIN_ROLES,
  isAuthenticated = false,
  className = ''
}) => {
  const [activeSection, setActiveSection] = useState(currentSection);
  const [showHamburgerMenu, setShowHamburgerMenu] = useState(false);
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

  // Handle window resize to detect mobile vs desktop
  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);
      if (!mobile) {
        setShowHamburgerMenu(false); // Close hamburger menu on desktop
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Close hamburger menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showHamburgerMenu && !event.target.closest('.mobile-nav-hamburger')) {
        setShowHamburgerMenu(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, [showHamburgerMenu]);

  const handleNavigation = (section, subsection = null) => {
    setActiveSection(section);
    setShowHamburgerMenu(false); // Close hamburger menu after navigation
    if (onNavigate) {
      onNavigate(section, subsection);
    }
  };

  const toggleHamburgerMenu = (e) => {
    e.stopPropagation();
    setShowHamburgerMenu(!showHamburgerMenu);
  };

  // Navigation items for both hamburger and bottom tab
  const navItems = [
    {
      id: 'home',
      label: 'Home',
      icon: (
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
          <polyline points="9,22 9,12 15,12 15,22"></polyline>
        </svg>
      ),
      iconFilled: (
        <svg viewBox="0 0 24 24" fill="currentColor" stroke="none">
          <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
          <polyline points="9,22 9,12 15,12 15,22"></polyline>
        </svg>
      ),
      description: 'Community Feed'
    },
    {
      id: 'stories',
      label: 'Stories',
      icon: (
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20"></path>
          <path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z"></path>
        </svg>
      ),
      iconFilled: (
        <svg viewBox="0 0 24 24" fill="currentColor" stroke="none">
          <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20"></path>
          <path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z"></path>
        </svg>
      ),
      description: 'Share & Discover'
    },
    {
      id: 'connect',
      label: 'Connect',
      icon: (
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
          <circle cx="9" cy="7" r="4"></circle>
          <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
          <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
        </svg>
      ),
      iconFilled: (
        <svg viewBox="0 0 24 24" fill="currentColor" stroke="none">
          <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
          <circle cx="9" cy="7" r="4"></circle>
          <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
          <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
        </svg>
      ),
      description: 'Community & Support'
    },
    {
      id: 'explore',
      label: 'Explore',
      icon: (
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <circle cx="11" cy="11" r="8"></circle>
          <path d="M21 21l-4.35-4.35"></path>
        </svg>
      ),
      iconFilled: (
        <svg viewBox="0 0 24 24" fill="currentColor" stroke="none">
          <circle cx="11" cy="11" r="8"></circle>
          <path d="M21 21l-4.35-4.35"></path>
        </svg>
      ),
      description: 'Discover Content'
    },
    {
      id: 'profile',
      label: 'Profile',
      icon: (
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
          <circle cx="12" cy="7" r="4"></circle>
        </svg>
      ),
      iconFilled: (
        <svg viewBox="0 0 24 24" fill="currentColor" stroke="none">
          <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
          <circle cx="12" cy="7" r="4"></circle>
        </svg>
      ),
      description: 'Your Account'
    }
  ];

  // Additional menu items for hamburger menu
  const additionalMenuItems = [
    {
      id: 'economic',
      label: 'Economic Hub',
      icon: (
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <line x1="12" y1="1" x2="12" y2="23"></line>
          <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
        </svg>
      ),
      description: 'Business & Opportunities'
    },
    {
      id: 'dialogue',
      label: 'Community Dialogue',
      icon: (
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
        </svg>
      ),
      description: 'Discussions & Forums'
    },
    {
      id: 'support',
      label: 'Support Requests',
      icon: (
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M9 12l2 2 4-4"></path>
          <path d="M21 12c.552 0 1-.448 1-1V8a2 2 0 0 0-2-2h-1V4a2 2 0 0 0-2-2H7a2 2 0 0 0-2 2v2H4a2 2 0 0 0-2 2v3c0 .552.448 1 1 1h1v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-2h1z"></path>
        </svg>
      ),
      description: 'Community Assistance'
    }
  ];

  if (!isAuthenticated) {
    return null; // Don't show navigation for unauthenticated users
  }

  return (
    <div className={`mobile-navigation ${className}`}>
      {/* Hamburger Menu Button (Mobile Only) */}
      {isMobile && (
        <button
          className={`mobile-nav-hamburger ${showHamburgerMenu ? 'mobile-nav-hamburger--active' : ''}`}
          onClick={toggleHamburgerMenu}
          aria-label="Toggle navigation menu"
          aria-expanded={showHamburgerMenu}
        >
          <span className="mobile-nav-hamburger__line"></span>
          <span className="mobile-nav-hamburger__line"></span>
          <span className="mobile-nav-hamburger__line"></span>
        </button>
      )}

      {/* Hamburger Menu Overlay */}
      {isMobile && showHamburgerMenu && (
        <>
          <div 
            className="mobile-nav-overlay"
            onClick={() => setShowHamburgerMenu(false)}
          />
          <div className="mobile-nav-hamburger-menu">
            <div className="mobile-nav-hamburger-header">
              <h3>Navigation</h3>
              <button
                className="mobile-nav-close"
                onClick={() => setShowHamburgerMenu(false)}
                aria-label="Close navigation menu"
              >
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </button>
            </div>
            <div className="mobile-nav-hamburger-content">
              {/* Main navigation items */}
              <div className="mobile-nav-section">
                <h4>Main</h4>
                {navItems.map((item) => (
                  <button
                    key={item.id}
                    className={`mobile-nav-hamburger-item ${
                      activeSection === item.id ? 'mobile-nav-hamburger-item--active' : ''
                    }`}
                    onClick={() => handleNavigation(item.id)}
                  >
                    <div className="mobile-nav-hamburger-icon">
                      {item.icon}
                    </div>
                    <div className="mobile-nav-hamburger-text">
                      <span className="mobile-nav-hamburger-label">{item.label}</span>
                      <span className="mobile-nav-hamburger-description">{item.description}</span>
                    </div>
                  </button>
                ))}
              </div>

              {/* Additional menu items */}
              <div className="mobile-nav-section">
                <h4>More</h4>
                {additionalMenuItems.map((item) => (
                  <button
                    key={item.id}
                    className={`mobile-nav-hamburger-item ${
                      activeSection === item.id ? 'mobile-nav-hamburger-item--active' : ''
                    }`}
                    onClick={() => handleNavigation(item.id)}
                  >
                    <div className="mobile-nav-hamburger-icon">
                      {item.icon}
                    </div>
                    <div className="mobile-nav-hamburger-text">
                      <span className="mobile-nav-hamburger-label">{item.label}</span>
                      <span className="mobile-nav-hamburger-description">{item.description}</span>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </>
      )}

      {/* Bottom Tab Bar (Mobile Only) */}
      {isMobile && (
        <div className="mobile-nav-bottom-tabs">
          {navItems.map((item) => (
            <button
              key={item.id}
              className={`mobile-nav-tab ${
                activeSection === item.id ? 'mobile-nav-tab--active' : ''
              }`}
              onClick={() => handleNavigation(item.id)}
              aria-label={`Navigate to ${item.label}`}
            >
              <div className="mobile-nav-tab-icon">
                {activeSection === item.id ? item.iconFilled : item.icon}
              </div>
              <span className="mobile-nav-tab-label">{item.label}</span>
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default MobileNavigation;
